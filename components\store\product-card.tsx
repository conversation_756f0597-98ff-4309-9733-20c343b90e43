"use client"

import { Card } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Lock, Clock, ImageIcon, ShoppingCart, ExternalLink } from "lucide-react"
import Image from "next/image"
import { useState } from "react"
import Link from "next/link"

interface ProductCardProps {
  id: number
  name: string
  rarity: string
  isLegacy: boolean
  description?: string | null
  imageUrl: string
  price: number
  type: string
  rarityIcon?: string
}

export function ProductCard({
  id,
  name,
  rarity,
  isLegacy,
  description,
  imageUrl,
  price,
  type,
  rarityIcon
}: ProductCardProps) {
  const [imageError, setImageError] = useState(false)

  // Get rarity color
  const getRarityColor = (rarity: string) => {
    switch (rarity.toLowerCase()) {
      case 'rare':
        return 'bg-blue-500/20 text-blue-300 border-blue-500/30'
      case 'epic':
        return 'bg-purple-500/20 text-purple-300 border-purple-500/30'
      case 'legendary':
        return 'bg-orange-500/20 text-orange-300 border-orange-500/30'
      case 'mythic':
        return 'bg-red-500/20 text-red-300 border-red-500/30'
      case 'ultimate':
        return 'bg-yellow-500/20 text-yellow-300 border-yellow-500/30'
      case 'exalted':
        return 'bg-pink-500/20 text-pink-300 border-pink-500/30'
      case 'transcendent':
        return 'bg-cyan-500/20 text-cyan-300 border-cyan-500/30'
      default:
        return 'bg-gray-500/20 text-gray-300 border-gray-500/30'
    }
  }

  return (
    <div className="group relative">
      <Card className="relative overflow-hidden bg-black/40 border-purple-500/20 hover:border-purple-400/40 transition-all duration-300 hover:brightness-125 aspect-square w-full min-w-[150px] rounded-none">
      {/* Background Image - Full Card */}
      <div className="absolute inset-0">
        {!imageError ? (
          <Image
            src={imageUrl}
            alt={name}
            fill
            className="object-cover transition-transform duration-300 group-hover:scale-110"
            sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
            onError={() => setImageError(true)}
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center bg-gray-800">
            <ImageIcon className="w-12 h-12 text-gray-500" />
          </div>
        )}
        {/* Dark overlay for better text readability */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-black/40" />
      </div>

      {/* Top-left legacy indicator */}
      {isLegacy && (
        <div className="absolute top-2 left-2 z-10 flex items-center space-x-1 bg-black/80 backdrop-blur-sm px-2 py-1">
          <Lock className="w-3 h-3 text-yellow-400" />
          <span className="text-xs font-medium text-yellow-400">Legacy</span>
        </div>
      )}

      {/* Top-right rarity badge */}
      <div className="absolute top-2 right-2 z-10">
        <Badge
          variant="outline"
          className={`text-xs font-medium ${getRarityColor(rarity)} backdrop-blur-sm flex items-center space-x-1`}
        >
          {rarityIcon && (
            <Image
              src={rarityIcon}
              alt={rarity}
              width={12}
              height={12}
              className="flex-shrink-0"
            />
          )}
          <span>{rarity}</span>
        </Badge>
      </div>

        {/* Bottom content overlay */}
        <div className="absolute bottom-0 left-0 right-0 p-3 z-10">
          {/* Item name */}
          <h3 className="font-bold text-white text-sm leading-tight mb-2 line-clamp-2">
            {name}
          </h3>

          {/* Price */}
          <div className="flex items-center">
            <span className="text-lg font-bold text-white">
              ${price}
            </span>
          </div>
        </div>
      </Card>

      {/* Action Layer - Slides down from under the card */}
      <div className="absolute top-full -left-px -right-px h-0 group-hover:h-12 bg-gradient-to-r from-black/95 to-black/95 backdrop-blur-sm border border-purple-500/20 border-t-0 rounded-b-lg transition-all duration-150 ease-out z-20 overflow-hidden">
        <div className="flex h-full rounded-b-lg overflow-hidden">
          {/* Cart Button - Left Half */}
          <button
            className="flex-1 h-full bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white font-medium flex items-center justify-center gap-2 transition-colors rounded-bl-lg will-change-transform"
            onClick={(e) => {
              e.preventDefault()
              // Add to cart logic here
              console.log('Added to cart:', name)
            }}
          >
            <ShoppingCart className="w-4 h-4 flex-shrink-0" />
            <span className="text-sm whitespace-nowrap">Cart</span>
          </button>

          {/* Separator Line */}
          <div className="w-px bg-purple-400/40 flex-shrink-0"></div>

          {/* View Details Button - Right Half */}
          <Link href={`/skin/${id}`} className="flex-1">
            <button className="w-full h-full bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white font-medium flex items-center justify-center gap-2 transition-colors rounded-br-lg will-change-transform">
              <ExternalLink className="w-4 h-4 flex-shrink-0" />
              <span className="text-sm whitespace-nowrap">View</span>
            </button>
          </Link>
        </div>
      </div>
    </div>
  )
}
